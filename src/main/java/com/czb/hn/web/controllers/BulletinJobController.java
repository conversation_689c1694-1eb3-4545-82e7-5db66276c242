package com.czb.hn.web.controllers;

import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.dto.bulletin.BulletinJobRequest;
import com.czb.hn.service.bulletin.BulletinJobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 简报任务控制器
 * 提供RESTful API接口调用简报任务服务
 */
@RestController
@RequestMapping("/bulletin-jobs")
@Tag(name = "简报任务管理", description = "简报任务的增删改查和启用禁用接口")
public class BulletinJobController {

    private static final Logger log = LoggerFactory.getLogger(BulletinJobController.class);
    

    private final BulletinJobService bulletinJobService;

    public BulletinJobController(BulletinJobService bulletinJobService) {
        this.bulletinJobService = bulletinJobService;
    }

    /**
     * 创建或更新简报任务
     */
    @PostMapping
    @Operation(summary = "创建或更新简报任务", description = "根据请求参数创建新的简报任务或更新已有任务")
    public ResponseEntity<ApiResponse<Long>> saveBulletinJob(
            @Parameter(description = "简报任务请求参数") @RequestBody BulletinJobRequest request) {
        try {
            Long jobId = bulletinJobService.saveBulletinJob(request);
            return ResponseEntity.ok(ApiResponse.success(jobId));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("保存简报任务失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ApiResponse.error("保存简报任务失败: " + e.getMessage()));
        }
    }
    
    /**
     * 启用简报任务
     */
    @PostMapping("/{id}/enable")
    @Operation(summary = "启用简报任务", description = "将指定ID的简报任务状态设置为启用")
    public ResponseEntity<ApiResponse<Void>> enableBulletinJob(
            @Parameter(description = "任务ID") @PathVariable Long id) {
        try {
            bulletinJobService.enableBulletinJob(id);
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("启用简报任务失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ApiResponse.error("启用简报任务失败: " + e.getMessage()));
        }
    }
    
    /**
     * 禁用简报任务
     */
    @PostMapping("/{id}/disable")
    @Operation(summary = "禁用简报任务", description = "将指定ID的简报任务状态设置为禁用")
    public ResponseEntity<ApiResponse<Void>> disableBulletinJob(
            @Parameter(description = "任务ID") @PathVariable Long id) {
        try {
            bulletinJobService.disableBulletinJob(id);
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("禁用简报任务失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ApiResponse.error("禁用简报任务失败: " + e.getMessage()));
        }
    }
    
    /**
     * 删除简报任务
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除简报任务", description = "删除指定ID的简报任务")
    public ResponseEntity<ApiResponse<Void>> deleteBulletinJob(
            @Parameter(description = "任务ID") @PathVariable Long id) {
        try {
            bulletinJobService.deleteBulletinJob(id);
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            log.error("删除简报任务失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ApiResponse.error("删除简报任务失败: " + e.getMessage()));
        }
    }


    /**
     * 批量配置方案下的简报任务（日报/周报/月报）
     */
    @PostMapping("/plan/{planId}/batch")
    @Operation(summary = "批量配置简报任务", description = "批量配置指定方案下的简报任务（日报/周报/月报）")
    public ResponseEntity<ApiResponse<List<Long>>> batchConfigBulletinJobs(
            @Parameter(description = "方案ID") @PathVariable Long planId,
            @Parameter(description = "简报任务请求参数列表") @RequestBody List<BulletinJobRequest> requests) {
        try {
            List<Long> jobIds = bulletinJobService.updateBulletinJobsByPlanId(planId, requests);
            return ResponseEntity.ok(ApiResponse.success(jobIds));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("批量配置失败: " + e.getMessage()));
        }
    }
} 