package com.czb.hn.web.controllers;

import com.czb.hn.dto.bulletin.BulletinGenerationRecordDto;
import com.czb.hn.dto.bulletin.BulletinPushRecordDto;
import com.czb.hn.dto.common.PageResult;
import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.service.bulletin.BulletinRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 简报记录控制器
 */
@RestController
@RequestMapping("/bulletin/records")
@Tag(name = "简报记录管理", description = "简报生成记录和推送记录的管理接口")
public class BulletinRecordController {
    
    private static final Logger log = LoggerFactory.getLogger(BulletinRecordController.class);
    
    private final BulletinRecordService bulletinRecordService;

    public BulletinRecordController(BulletinRecordService bulletinRecordService) {
        this.bulletinRecordService = bulletinRecordService;
    }

    /**
     * 获取简报生成记录列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 简报生成记录分页结果
     */
    @GetMapping
    @Operation(summary = "获取简报生成记录列表", description = "根据时间范围查询简报生成记录，支持分页")
    public ResponseEntity<ApiResponse<PageResult<BulletinGenerationRecordDto>>> getGenerationRecords(
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        try {
            // 如果未指定时间范围，默认查询最近30天的记录
            if (startTime == null) {
                startTime = LocalDateTime.now().minusDays(30);
            }
            if (endTime == null) {
                endTime = LocalDateTime.now();
            }
            
            // 调用Service层获取分页DTO数据
            PageResult<BulletinGenerationRecordDto> pageResult = bulletinRecordService.getGenerationRecords(
                startTime, 
                endTime, 
                page, 
                size
            );
            
            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success(pageResult));
        } catch (Exception e) {
            log.error("获取简报生成记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取简报生成记录失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据ID获取简报生成记录
     *
     * @param id 记录ID
     * @return 简报生成记录
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取简报生成记录", description = "获取指定ID的简报生成记录详情")
    public ResponseEntity<ApiResponse<BulletinGenerationRecordDto>> getGenerationRecordById(
            @Parameter(description = "记录ID") @PathVariable Long id) {
        try {
            // 调用Service层获取DTO
            BulletinGenerationRecordDto record = bulletinRecordService.getGenerationRecordById(id);
            
            // 检查记录是否存在
            if (record == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("找不到指定的生成记录: " + id));
            }
            
            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success(record));
        } catch (Exception e) {
            log.error("获取简报生成记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取简报生成记录失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据任务ID获取简报生成记录
     *
     * @param jobId 任务ID
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 简报生成记录分页结果
     */
    @GetMapping("/job/{jobId}")
    @Operation(summary = "根据任务ID获取简报生成记录", description = "获取指定任务ID的所有简报生成记录，支持分页")
    public ResponseEntity<ApiResponse<PageResult<BulletinGenerationRecordDto>>> getGenerationRecordsByJobId(
            @Parameter(description = "任务ID") @PathVariable Long jobId,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        try {
            // 调用Service层获取分页DTO数据
            PageResult<BulletinGenerationRecordDto> pageResult = bulletinRecordService.getGenerationRecordsByJobId(
                jobId, 
                page, 
                size
            );
            
            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success(pageResult));
        } catch (Exception e) {
            log.error("获取简报生成记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取简报生成记录失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据简报类型获取简报生成记录
     *
     * @param bulletinType 简报类型
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 简报生成记录分页结果
     */
    @GetMapping("/type/{bulletinType}")
    @Operation(summary = "根据简报类型获取生成记录", description = "获取指定类型（日报、周报、月报）的简报生成记录，支持分页")
    public ResponseEntity<ApiResponse<PageResult<BulletinGenerationRecordDto>>> getGenerationRecordsByType(
            @Parameter(description = "简报类型（DAILY、WEEKLY、MONTHLY）") @PathVariable String bulletinType,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        try {
            // 调用Service层获取分页DTO数据
            PageResult<BulletinGenerationRecordDto> pageResult = bulletinRecordService.getGenerationRecordsByType(
                bulletinType, 
                page, 
                size
            );
            
            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success(pageResult));
        } catch (Exception e) {
            log.error("获取简报生成记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取简报生成记录失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取最近的简报生成记录
     *
     * @param limit 限制数量
     * @return 简报生成记录列表
     */
    @GetMapping("/recent")
    @Operation(summary = "获取最近的简报生成记录", description = "获取最近生成的简报记录，可指定返回数量")
    public ResponseEntity<ApiResponse<List<BulletinGenerationRecordDto>>> getRecentGenerationRecords(
            @Parameter(description = "返回记录数量") @RequestParam(defaultValue = "10") int limit) {
        try {
            // 调用Service层获取DTO列表
            List<BulletinGenerationRecordDto> records = bulletinRecordService.getRecentGenerationRecords(limit);
            
            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success( records));
        } catch (Exception e) {
            log.error("获取最近简报生成记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取最近简报生成记录失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据生成记录ID获取推送记录
     *
     * @param generationId 生成记录ID
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 推送记录分页结果
     */
    @GetMapping("/{generationId}/pushes")
    @Operation(summary = "获取简报推送记录", description = "获取指定简报生成记录的所有推送记录，支持分页")
    public ResponseEntity<ApiResponse<PageResult<BulletinPushRecordDto>>> getPushRecordsByGenerationId(
            @Parameter(description = "生成记录ID") @PathVariable Long generationId,
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size) {
        try {
            // 调用Service层获取分页DTO数据
            PageResult<BulletinPushRecordDto> pageResult = bulletinRecordService.getPushRecordsByGenerationId(
                generationId, 
                page, 
                size
            );
            
            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success(pageResult));
        } catch (Exception e) {
            log.error("获取推送记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取推送记录失败: " + e.getMessage()));
        }
    }
    
    /**
     * 手动推送简报
     *
     * @param generationId 生成记录ID
     * @param request 请求参数，包含emailReceivers和smsReceivers
     * @return 推送记录ID列表
     */
    @PostMapping("/{generationId}/push")
    @Operation(summary = "手动推送简报", description = "手动推送指定的简报到邮箱或短信")
    public ResponseEntity<ApiResponse<List<Long>>> manualPushBulletin(
            @Parameter(description = "生成记录ID") @PathVariable Long generationId,
            @Parameter(description = "接收者信息，包含emailReceivers和smsReceivers字段") @RequestBody Map<String, List<String>> request) {
        try {
            // 从请求体中获取参数
            List<String> emailReceivers = request.get("emailReceivers");
            List<String> smsReceivers = request.get("smsReceivers");
            
            // 调用Service层执行推送
            List<Long> pushIds = bulletinRecordService.manualPushBulletin(generationId, emailReceivers, smsReceivers);
            
            // 返回成功响应
            return ResponseEntity.ok(ApiResponse.success(pushIds));
        } catch (Exception e) {
            log.error("手动推送简报失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("手动推送简报失败: " + e.getMessage()));
        }
    }
    
    /**
     * 下载简报内容
     *
     * @param generationId 生成记录ID
     * @return 简报内容
     */
    @GetMapping("/{generationId}/download")
    @Operation(summary = "下载简报内容", description = "下载指定简报生成记录的PDF内容")
    public ResponseEntity<byte[]> downloadBulletinContent(
            @Parameter(description = "生成记录ID") @PathVariable Long generationId) {
        try {
            // 调用Service层获取DTO
            BulletinGenerationRecordDto record = bulletinRecordService.getGenerationRecordById(generationId);
            
            // 检查记录是否存在
            if (record == null) {
                return ResponseEntity.notFound().build();
            }
            
            // 调用Service层获取简报内容
            byte[] content = bulletinRecordService.getBulletinContent(generationId);
            
            // 检查内容是否为空
            if (content == null || content.length == 0) {
                return ResponseEntity.noContent().build();
            }
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", record.bulletinTitle() + ".pdf");
            
            // 返回文件内容
            return new ResponseEntity<>(content, headers, HttpStatus.OK);
        } catch (Exception e) {
            log.error("下载简报内容失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
} 