package com.czb.hn.web.controllers;

import com.czb.hn.config.ScheduleConfig;
import com.czb.hn.dto.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 定时任务管理控制器
 * 提供定时任务配置的查询和动态控制功能
 */
@RestController
@RequestMapping("/admin/schedule")
@Tag(name = "定时任务管理", description = "定时任务配置查询和控制API")
@Slf4j
public class ScheduleManagementController {

    @Autowired
    private ScheduleConfig scheduleConfig;

    /**
     * 获取所有定时任务的配置状态
     */
    @GetMapping("/status")
    @Operation(summary = "获取定时任务状态", description = "获取所有定时任务的配置状态和环境信息")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "获取成功", content = @Content(mediaType = "application/json", schema = @Schema(implementation = Map.class)))
    })
    public ResponseEntity<ApiResponse<Map<String, Object>>> getScheduleStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            
            // 数据收集器状态
            Map<String, Object> collectorStatus = new HashMap<>();
            collectorStatus.put("enabled", scheduleConfig.getCollector().isEnabled());
            collectorStatus.put("cron", scheduleConfig.getCollector().getCron());
            collectorStatus.put("environment", scheduleConfig.getCollector().getEnvironment());
            collectorStatus.put("batchSize", scheduleConfig.getCollector().getBatchSize());
            collectorStatus.put("maxRetries", scheduleConfig.getCollector().getMaxRetries());
            collectorStatus.put("retryInterval", scheduleConfig.getCollector().getRetryInterval());
            status.put("collector", collectorStatus);
            
            // 数据清洗器状态
            Map<String, Object> cleanerStatus = new HashMap<>();
            cleanerStatus.put("enabled", scheduleConfig.getCleaner().isEnabled());
            cleanerStatus.put("cron", scheduleConfig.getCleaner().getCron());
            cleanerStatus.put("environment", scheduleConfig.getCleaner().getEnvironment());
            cleanerStatus.put("batchSize", scheduleConfig.getCleaner().getBatchSize());
            status.put("cleaner", cleanerStatus);
            
            // 数据聚合器状态
            Map<String, Object> aggregatorStatus = new HashMap<>();
            aggregatorStatus.put("enabled", scheduleConfig.getAggregator().isEnabled());
            aggregatorStatus.put("cron", scheduleConfig.getAggregator().getCron());
            aggregatorStatus.put("environment", scheduleConfig.getAggregator().getEnvironment());
            status.put("aggregator", aggregatorStatus);
            
            // Elasticsearch同步状态
            Map<String, Object> esSyncStatus = new HashMap<>();
            esSyncStatus.put("enabled", scheduleConfig.getElasticsearchSync().isEnabled());
            esSyncStatus.put("cron", scheduleConfig.getElasticsearchSync().getCron());
            esSyncStatus.put("environment", scheduleConfig.getElasticsearchSync().getEnvironment());
            esSyncStatus.put("batchSize", scheduleConfig.getElasticsearchSync().getBatchSize());
            status.put("elasticsearchSync", esSyncStatus);
            
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", "获取定时任务状态成功", status));
            
        } catch (Exception e) {
            log.error("Error getting schedule status: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(new ApiResponse<>("ERROR", "获取定时任务状态失败: " + e.getMessage(), null));
        }
    }

    /**
     * 动态启用/禁用数据收集器定时任务
     */
    @PostMapping("/collector/toggle")
    @Operation(summary = "切换数据收集器状态", description = "动态启用或禁用数据收集器定时任务")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "操作成功"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "请求参数错误")
    })
    public ResponseEntity<ApiResponse<String>> toggleCollector(
            @Parameter(description = "是否启用", required = true) @RequestParam boolean enabled) {
        try {
            boolean oldStatus = scheduleConfig.getCollector().isEnabled();
            scheduleConfig.getCollector().setEnabled(enabled);
            
            String message = String.format("数据收集器定时任务已%s (环境: %s, 原状态: %s)", 
                    enabled ? "启用" : "禁用", 
                    scheduleConfig.getCollector().getEnvironment(),
                    oldStatus ? "启用" : "禁用");
            
            log.info(message);
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", message, null));
            
        } catch (Exception e) {
            log.error("Error toggling collector: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>("ERROR", "切换数据收集器状态失败: " + e.getMessage(), null));
        }
    }

    /**
     * 动态启用/禁用数据清洗器定时任务
     */
    @PostMapping("/cleaner/toggle")
    @Operation(summary = "切换数据清洗器状态", description = "动态启用或禁用数据清洗器定时任务")
    public ResponseEntity<ApiResponse<String>> toggleCleaner(
            @Parameter(description = "是否启用", required = true) @RequestParam boolean enabled) {
        try {
            boolean oldStatus = scheduleConfig.getCleaner().isEnabled();
            scheduleConfig.getCleaner().setEnabled(enabled);
            
            String message = String.format("数据清洗器定时任务已%s (环境: %s, 原状态: %s)", 
                    enabled ? "启用" : "禁用", 
                    scheduleConfig.getCleaner().getEnvironment(),
                    oldStatus ? "启用" : "禁用");
            
            log.info(message);
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", message, null));
            
        } catch (Exception e) {
            log.error("Error toggling cleaner: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>("ERROR", "切换数据清洗器状态失败: " + e.getMessage(), null));
        }
    }

    /**
     * 动态启用/禁用数据聚合器定时任务
     */
    @PostMapping("/aggregator/toggle")
    @Operation(summary = "切换数据聚合器状态", description = "动态启用或禁用数据聚合器定时任务")
    public ResponseEntity<ApiResponse<String>> toggleAggregator(
            @Parameter(description = "是否启用", required = true) @RequestParam boolean enabled) {
        try {
            boolean oldStatus = scheduleConfig.getAggregator().isEnabled();
            scheduleConfig.getAggregator().setEnabled(enabled);
            
            String message = String.format("数据聚合器定时任务已%s (环境: %s, 原状态: %s)", 
                    enabled ? "启用" : "禁用", 
                    scheduleConfig.getAggregator().getEnvironment(),
                    oldStatus ? "启用" : "禁用");
            
            log.info(message);
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", message, null));
            
        } catch (Exception e) {
            log.error("Error toggling aggregator: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>("ERROR", "切换数据聚合器状态失败: " + e.getMessage(), null));
        }
    }

    /**
     * 动态启用/禁用Elasticsearch同步定时任务
     */
    @PostMapping("/elasticsearch-sync/toggle")
    @Operation(summary = "切换ES同步状态", description = "动态启用或禁用Elasticsearch同步定时任务")
    public ResponseEntity<ApiResponse<String>> toggleElasticsearchSync(
            @Parameter(description = "是否启用", required = true) @RequestParam boolean enabled) {
        try {
            boolean oldStatus = scheduleConfig.getElasticsearchSync().isEnabled();
            scheduleConfig.getElasticsearchSync().setEnabled(enabled);
            
            String message = String.format("Elasticsearch同步定时任务已%s (环境: %s, 原状态: %s)", 
                    enabled ? "启用" : "禁用", 
                    scheduleConfig.getElasticsearchSync().getEnvironment(),
                    oldStatus ? "启用" : "禁用");
            
            log.info(message);
            return ResponseEntity.ok(new ApiResponse<>("SUCCESS", message, null));
            
        } catch (Exception e) {
            log.error("Error toggling elasticsearch sync: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponse<>("ERROR", "切换ES同步状态失败: " + e.getMessage(), null));
        }
    }
}
