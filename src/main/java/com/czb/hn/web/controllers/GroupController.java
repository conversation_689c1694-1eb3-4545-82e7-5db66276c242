package com.czb.hn.web.controllers;

import com.czb.hn.dto.response.ApiResponse;
import com.czb.hn.dto.user.GroupInfo;
import com.czb.hn.service.business.GroupCacheService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 组织管理控制器
 * 提供组织结构相关的API
 */
@RestController
@RequestMapping("/groups")
@Tag(name = "组织管理", description = "提供组织结构相关的API")
public class GroupController {

    private static final Logger logger = LoggerFactory.getLogger(GroupController.class);

    @Autowired
    private GroupCacheService groupCacheService;

    @GetMapping
    @Operation(summary = "获取所有组织", description = "获取系统中的所有组织信息")
    public ResponseEntity<ApiResponse<List<GroupInfo>>> getAllGroups() {
        try {
            List<GroupInfo> groups = groupCacheService.getAllGroups();
            return ResponseEntity.ok(ApiResponse.success(groups));
        } catch (Exception e) {
            logger.error("获取组织列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取组织列表失败: " + e.getMessage()));
        }
    }

    @GetMapping("/id/{groupId}")
    @Operation(summary = "根据ID获取组织", description = "根据组织ID获取组织信息")
    public ResponseEntity<ApiResponse<GroupInfo>> getGroupById(
            @Parameter(description = "组织ID", required = true) @PathVariable String groupId) {
        try {
            Optional<GroupInfo> group = groupCacheService.getGroupById(groupId);
            return group.map(g -> ResponseEntity.ok(ApiResponse.success(g)))
                    .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(ApiResponse.error("未找到ID为 " + groupId + " 的组织")));
        } catch (Exception e) {
            logger.error("根据ID获取组织失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取组织信息失败: " + e.getMessage()));
        }
    }

    @GetMapping("/code/{groupCode}")
    @Operation(summary = "根据代码获取组织", description = "根据组织代码获取组织信息")
    public ResponseEntity<ApiResponse<GroupInfo>> getGroupByCode(
            @Parameter(description = "组织代码", required = true) @PathVariable String groupCode) {
        try {
            Optional<GroupInfo> group = groupCacheService.getGroupByCode(groupCode);
            return group.map(g -> ResponseEntity.ok(ApiResponse.success(g)))
                    .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(ApiResponse.error("未找到代码为 " + groupCode + " 的组织")));
        } catch (Exception e) {
            logger.error("根据代码获取组织失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取组织信息失败: " + e.getMessage()));
        }
    }

    @GetMapping("/mapping")
    @Operation(summary = "获取ID-代码映射", description = "获取组织ID与组织代码的映射关系")
    public ResponseEntity<ApiResponse<Map<String, String>>> getGroupMapping() {
        try {
            Map<String, String> mapping = groupCacheService.getGroupIdToCodeMapping();
            return ResponseEntity.ok(ApiResponse.success(mapping));
        } catch (Exception e) {
            logger.error("获取组织映射关系失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取组织映射关系失败: " + e.getMessage()));
        }
    }

    @GetMapping("/refresh")
    @Operation(summary = "刷新组织缓存", description = "手动刷新组织信息缓存")
    public ResponseEntity<ApiResponse<Void>> refreshGroupsCache() {
        try {
            groupCacheService.refreshCache();
            return ResponseEntity.ok(ApiResponse.success(null));
        } catch (Exception e) {
            logger.error("刷新组织缓存失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("刷新组织缓存失败: " + e.getMessage()));
        }
    }
} 