package com.czb.hn.jpa.securadar.entity;

import com.czb.hn.enums.NotificationStatus;
import com.czb.hn.enums.NotificationType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * Alert Notification Queue Entity
 * Stores notification scheduling information separate from alert generation
 * Enables flexible notification timing based on reception settings
 */
@Entity
@Table(name = "alert_notification_queue", indexes = {
        @Index(name = "idx_scheduled_status", columnList = "scheduled_time,status"),
        @Index(name = "idx_enterprise_scheduled", columnList = "enterprise_id,scheduled_time"),
        @Index(name = "idx_plan_id", columnList = "plan_id"),
        @Index(name = "idx_configuration_id", columnList = "configuration_id"),
        @Index(name = "idx_status_type", columnList = "status,notification_type"),
        @Index(name = "idx_enterprise_status", columnList = "enterprise_id,status")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AlertNotificationQueue {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "plan_id", nullable = false)
    private Long planId;

    /**
     * Configuration ID that generated this notification
     */
    @Column(name = "configuration_id", nullable = false)
    private Long configurationId;

    /**
     * Enterprise ID for tenant isolation
     */
    @Column(name = "enterprise_id", nullable = false, length = 255)
    private String enterpriseId;

    /**
     * When this notification should be sent
     */
    @Column(name = "scheduled_time", nullable = false)
    private LocalDateTime scheduledTime;

    /**
     * Current status of the notification
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    @Builder.Default
    private NotificationStatus status = NotificationStatus.PENDING;

    /**
     * Recipients information stored as JSON
     * Contains email addresses, phone numbers, and usernames
     */
    @Column(name = "recipients", columnDefinition = "JSON", nullable = false)
    private String recipients;

    /**
     * Type of notification (ALERT, INFO_PUSH, NO_ALERT)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "notification_type", nullable = false, length = 20)
    private NotificationType notificationType;

    /**
     * Reception settings snapshot for this notification
     */
    @Column(name = "reception_settings", columnDefinition = "JSON")
    private String receptionSettings;

    /**
     * Number of processing attempts
     */
    @Column(name = "attempt_count", nullable = false)
    @Builder.Default
    private Integer attemptCount = 0;

    /**
     * Last error message if processing failed
     */
    @Column(name = "last_error", columnDefinition = "TEXT")
    private String lastError;

    /**
     * When the notification was actually processed
     */
    @Column(name = "processed_at")
    private LocalDateTime processedAt;

    /**
     * Next retry time if processing failed
     */
    @Column(name = "next_retry_time")
    private LocalDateTime nextRetryTime;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * User or system that created this notification
     */
    @Column(name = "created_by", length = 255)
    private String createdBy;


    /**
     * Check if this notification is ready to be processed
     */
    public boolean isReadyToProcess() {
        return status.canProcess() &&
                scheduledTime.isBefore(LocalDateTime.now()) &&
                attemptCount < 3; // Maximum 3 attempts
    }

    /**
     * Check if this notification can be retried
     */
    public boolean canRetry() {
        return status.canRetry() &&
                attemptCount < 3 &&
                (nextRetryTime == null || nextRetryTime.isBefore(LocalDateTime.now()));
    }

    /**
     * Mark notification as processing
     */
    public void markAsProcessing() {
        this.status = NotificationStatus.PROCESSING;
        this.attemptCount++;
        this.processedAt = LocalDateTime.now();
    }

    /**
     * Mark notification as completed
     */
    public void markAsCompleted() {
        this.status = NotificationStatus.COMPLETED;
        this.processedAt = LocalDateTime.now();
        this.lastError = null;
        this.nextRetryTime = null;
    }

    /**
     * Mark notification as failed and schedule retry
     */
    public void markAsFailed(String errorMessage) {
        this.status = NotificationStatus.FAILED;
        this.lastError = errorMessage;

        // Schedule next retry (exponential backoff)
        if (attemptCount < 3) {
            long delayMinutes = (long) Math.pow(2, attemptCount) * 5; // 5, 10, 20 minutes
            this.nextRetryTime = LocalDateTime.now().plusMinutes(delayMinutes);
        }
    }

    /**
     * Check if this is a no-alert notification
     */
    public boolean isNoAlertNotification() {
        return  notificationType == NotificationType.NO_ALERT;
    }


    /**
     * Get notification content description
     */
    public String getNotificationDescription() {
        if (isNoAlertNotification()) {
            return "无预警通知";
        } else if (notificationType == NotificationType.INFO_PUSH) {
            return "信息补推";
        } else {
            return "预警通知";
        }
    }
}
