package com.czb.hn.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * Utility class for handling batch alert operations
 * Provides methods for serializing/deserializing alert ID lists
 */
@Slf4j
public class AlertBatchUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Serialize a list of alert IDs to JSON string
     * 
     * @param alertIds List of alert IDs
     * @return JSON string representation
     */
    public static String serializeAlertIds(List<Long> alertIds) {
        if (alertIds == null || alertIds.isEmpty()) {
            return null;
        }
        
        try {
            return objectMapper.writeValueAsString(alertIds);
        } catch (Exception e) {
            log.error("Failed to serialize alert IDs: {}", alertIds, e);
            return null;
        }
    }

    /**
     * Deserialize JSON string to list of alert IDs
     * 
     * @param alertIdsJson JSON string containing alert IDs
     * @return List of alert IDs
     */
    public static List<Long> deserializeAlertIds(String alertIdsJson) {
        if (alertIdsJson == null || alertIdsJson.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            return objectMapper.readValue(alertIdsJson, new TypeReference<List<Long>>() {});
        } catch (Exception e) {
            log.error("Failed to deserialize alert IDs from JSON: {}", alertIdsJson, e);
            return new ArrayList<>();
        }
    }

    /**
     * Add an alert ID to existing JSON string
     * 
     * @param existingAlertIdsJson Existing JSON string
     * @param newAlertId New alert ID to add
     * @return Updated JSON string
     */
    public static String addAlertId(String existingAlertIdsJson, Long newAlertId) {
        if (newAlertId == null) {
            return existingAlertIdsJson;
        }
        
        List<Long> alertIds = deserializeAlertIds(existingAlertIdsJson);
        if (!alertIds.contains(newAlertId)) {
            alertIds.add(newAlertId);
        }
        
        return serializeAlertIds(alertIds);
    }

    /**
     * Get count of alerts from JSON string
     * 
     * @param alertIdsJson JSON string containing alert IDs
     * @return Number of alerts
     */
    public static int getAlertCount(String alertIdsJson) {
        List<Long> alertIds = deserializeAlertIds(alertIdsJson);
        return alertIds.size();
    }

    /**
     * Check if JSON string contains any alert IDs
     * 
     * @param alertIdsJson JSON string to check
     * @return true if contains alerts, false otherwise
     */
    public static boolean hasAlerts(String alertIdsJson) {
        return getAlertCount(alertIdsJson) > 0;
    }

    /**
     * Create a batch summary description
     * 
     * @param alertIdsJson JSON string containing alert IDs
     * @return Human-readable description
     */
    public static String createBatchDescription(String alertIdsJson) {
        int count = getAlertCount(alertIdsJson);
        if (count == 0) {
            return "无预警";
        } else if (count == 1) {
            return "单个预警";
        } else {
            return "批量预警(" + count + "条)";
        }
    }
}
