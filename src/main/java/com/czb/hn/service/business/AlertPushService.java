package com.czb.hn.service.business;

import com.czb.hn.dto.alert.AlertPushDetailCreateDto;
import com.czb.hn.dto.alert.AlertPushDetailResponseDto;
import com.czb.hn.jpa.securadar.entity.AlertResult;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Alert Push Service Interface
 * Manages alert notification push operations including creation, querying, and
 * actual push delivery
 * Supports multi-tenant isolation and various notification channels
 */
public interface AlertPushService {

        /**
         * Create a new push detail record
         * 
         * @param createDto Push detail creation data
         * @return Created push detail response
         */
        AlertPushDetailResponseDto createPushDetail(AlertPushDetailCreateDto createDto);

        /**
         * Generate push detail records for an alert
         * Creates 3 push records: USERNAME+SYSTEM, PHONE+SMS, EMAIL+EMAIL
         * 
         * @param alertResult Alert result to generate push records for
         * @param username    Username for system push
         * @param phoneNumber Phone number for SMS push
         * @param email       Email address for email push
         * @return List of created push detail records
         */
        List<AlertPushDetailResponseDto> generatePushRecordsForAlert(
                        AlertResult alertResult, String username, String phoneNumber, String email);

        /**
         * Get push details by alert ID
         * 
         * @param alertId Alert ID to search for
         * @return List of push details for the alert
         */
        List<AlertPushDetailResponseDto> getPushDetailsByAlertId(Long alertId);

        /**
         * Retry failed push operations
         * 
         * @param pushDetailId Push detail ID to retry
         * @return Updated push detail response
         */
        AlertPushDetailResponseDto retryPush(Long pushDetailId);

        /**
         * Send email notification (placeholder implementation)
         * 
         * @param email   Email address
         * @param subject Email subject
         * @param content Email content
         * @return Async result of email sending operation
         */
        CompletableFuture<Boolean> sendEmailNotification(String email, String subject, String content);

        /**
         * Send SMS notification (placeholder implementation)
         * 
         * @param phoneNumber Phone number
         * @param message     SMS message content
         * @return Async result of SMS sending operation
         */
        CompletableFuture<Boolean> sendSmsNotification(String phoneNumber, String message);

        /**
         * Send system notification (placeholder implementation)
         * 
         * @param username Username
         * @param title    Notification title
         * @param content  Notification content
         * @return Async result of system notification operation
         */
        CompletableFuture<Boolean> sendSystemNotification(String username, String title, String content);

        /**
         * Process push notifications for an alert
         * Generates push records and attempts to send notifications
         * 
         * @param alertResult Alert result to process
         * @param username    Username for system notification
         * @param phoneNumber Phone number for SMS
         * @param email       Email address for email notification
         * @return Async result of push processing
         */
        CompletableFuture<List<AlertPushDetailResponseDto>> processPushNotifications(
                        AlertResult alertResult, String username, String phoneNumber, String email);

        /**
         * Clean up old push detail records
         *
         * @param daysToKeep Number of days to keep records
         * @return Number of records deleted
         */
        long cleanupOldPushDetails(int daysToKeep);

        /**
         * Create push records for an alert and recipient (for batch notifications)
         *
         * @param alert     Alert result to create push records for
         * @param recipient Recipient information
         */
        void createPushRecordsForAlert(AlertResult alert, Object recipient);
}
