package com.czb.hn.service.business.impl;

import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.alert.AlertPushDetailCreateDto;
import com.czb.hn.dto.alert.AlertPushDetailResponseDto;
import com.czb.hn.jpa.securadar.entity.AlertPushDetail;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import com.czb.hn.jpa.securadar.repository.AlertPushDetailRepository;
import com.czb.hn.service.business.AlertConfigurationConsumerService;
import com.czb.hn.service.business.AlertPushService;
import com.czb.hn.service.business.PlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Alert Push Service Implementation
 * Manages alert notification push operations with multi-tenant support
 */
@Service
@Slf4j
@Transactional
public class AlertPushServiceImpl implements AlertPushService {

    @Value("${alert.message.sms.template.content}")
    private String smsContentTemplate;
    @Value("${alert.message.sms.template.id}")
    private Long smsTemplateId;
    @Value("${alert.message.webHookUrl}")
    private String webHookUrl;

    @Autowired
    private AlertPushDetailRepository alertPushDetailRepository;

    @Autowired
    private com.czb.hn.util.SmsUtil smsUtil;

    @Autowired
    private AlertConfigurationConsumerService alertConfigConsumerService;

    @Autowired
    private PlanService planService;

    @Override
    public AlertPushDetailResponseDto createPushDetail(AlertPushDetailCreateDto createDto) {
        log.info("Creating push detail for alert ID: {}, type: {}", createDto.alertId(), createDto.pushType());

        try {
            AlertPushDetail pushDetail = AlertPushDetail.builder()
                    .enterpriseId(createDto.enterpriseId())
                    .planId(createDto.planId())
                    .alertConfigSnapshotId(createDto.alertConfigSnapshotId())
                    .accountInfo(createDto.accountInfo())
                    .pushType(createDto.pushType())
                    .pushStatus(createDto.pushStatus())
                    .pushTime(createDto.pushTime())
                    .errorMessage(createDto.errorMessage())
                    .pushDetails(createDto.pushDetails())
                    .retryCount(0)
                    .createdBy(createDto.createdBy())
                    .build();

            AlertPushDetail saved = alertPushDetailRepository.save(pushDetail);
            log.info("Successfully created push detail with ID: {}", saved.getId());

            return mapToResponseDto(saved);
        } catch (Exception e) {
            log.error("Failed to create push detail for alert ID: {}", createDto.alertId(), e);
            throw new RuntimeException("Failed to create push detail: " + e.getMessage(), e);
        }
    }

    @Override
    public List<AlertPushDetailResponseDto> generatePushRecordsForAlert(
            AlertResult alertResult, String username, String phoneNumber, String email) {

        log.info("Generating push records for alert ID: {}, enterprise: {}",
                alertResult.getId(), alertResult.getEnterpriseId());

        List<AlertPushDetailResponseDto> pushRecords = new ArrayList<>();
        LocalDateTime pushTime = LocalDateTime.now();

        try {
            // Create SMS push record for phone number
            if (phoneNumber != null && !phoneNumber.isBlank()) {
                AlertPushDetailCreateDto smsPush = new AlertPushDetailCreateDto(
                        alertResult.getId(),
                        alertResult.getPlanId(),
                        alertResult.getConfigurationId(),
                        alertResult.getEnterpriseId(),
                        phoneNumber,
                        PushType.SMS,
                        PushStatus.SUCCESS, // Default to success, will be updated based on actual push result
                        pushTime,
                        null,
                        "短信通知已生成",
                        "system");
                pushRecords.add(createPushDetail(smsPush));
            }

            // Create EMAIL push record for email address
            if (email != null && !email.isBlank()) {
                AlertPushDetailCreateDto emailPush = new AlertPushDetailCreateDto(
                        alertResult.getId(),
                        alertResult.getPlanId(),
                        alertResult.getConfigurationId(),
                        alertResult.getEnterpriseId(),
                        email,
                        PushType.EMAIL,
                        PushStatus.SUCCESS, // Default to success, will be updated based on actual push result
                        pushTime,
                        null,
                        "邮件通知已生成",
                        "system");
                pushRecords.add(createPushDetail(emailPush));
            }

            log.info("Successfully generated {} push records for alert ID: {}",
                    pushRecords.size(), alertResult.getId());

            return pushRecords;
        } catch (Exception e) {
            log.error("Failed to generate push records for alert ID: {}", alertResult.getId(), e);
            throw new RuntimeException("Failed to generate push records: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<AlertPushDetailResponseDto> getPushDetailsByAlertId(Long alertId) {
        log.debug("Getting push details for alert ID: {}", alertId);

        try {
            List<AlertPushDetail> pushDetails = alertPushDetailRepository.findByAlertIdOrderByPushTimeDesc(alertId);
            return pushDetails.stream()
                    .map(this::mapToResponseDto)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to get push details for alert ID: {}", alertId, e);
            throw new RuntimeException("Failed to get push details: " + e.getMessage(), e);
        }
    }

    /**
     * Map AlertPushDetail entity to response DTO
     */
    private AlertPushDetailResponseDto mapToResponseDto(AlertPushDetail pushDetail) {
        return new AlertPushDetailResponseDto(
                pushDetail.getId(),
                pushDetail.getEnterpriseId(),
                pushDetail.getAccountInfo(),
                pushDetail.getPushType(),
                pushDetail.getPushStatus(),
                pushDetail.getPushTime(),
                pushDetail.getErrorMessage(),
                pushDetail.getPushDetails(),
                pushDetail.getRetryCount(),
                pushDetail.getLastRetryTime(),
                pushDetail.getCreatedAt(),
                pushDetail.getUpdatedAt(),
                pushDetail.getCreatedBy());
    }

    @Override
    public AlertPushDetailResponseDto retryPush(Long pushDetailId) {
        log.info("Retrying push for push detail ID: {}", pushDetailId);

        try {
            AlertPushDetail pushDetail = alertPushDetailRepository.findById(pushDetailId)
                    .orElseThrow(() -> new IllegalArgumentException("Push detail not found with ID: " + pushDetailId));

            if (!pushDetail.canRetry()) {
                throw new IllegalArgumentException("Push detail cannot be retried (max retries reached or not failed)");
            }

            // Increment retry count
            pushDetail.incrementRetryCount();

            // Attempt to resend based on push type
            boolean success = false;
            String errorMessage = null;
            String pushDetails = null;

            try {
                switch (pushDetail.getPushType()) {
                    case EMAIL:
                        success = sendEmailNotification(
                                pushDetail.getAccountInfo(),
                                "Alert Notification",
                                "Alert content").get();
                        pushDetails = success ? "邮件重发成功" : "邮件重发失败";
                        break;
                    case SMS:
                        success = sendSmsNotification(
                                pushDetail.getAccountInfo(),
                                "Alert notification message").get();
                        pushDetails = success ? "短信重发成功" : "短信重发失败";
                        break;
                }
            } catch (Exception e) {
                log.error("Failed to retry push for ID: {}", pushDetailId, e);
                success = false;
                errorMessage = "重试失败: " + e.getMessage();
                pushDetails = "重试过程中发生错误";
            }

            // Update push detail
            pushDetail.setPushStatus(success ? PushStatus.SUCCESS : PushStatus.FAILURE);
            pushDetail.setErrorMessage(success ? null : errorMessage);
            pushDetail.setPushDetails(pushDetails);
            pushDetail.setPushTime(LocalDateTime.now());

            AlertPushDetail saved = alertPushDetailRepository.save(pushDetail);
            log.info("Successfully retried push for ID: {}, result: {}", pushDetailId, success);

            return mapToResponseDto(saved);
        } catch (Exception e) {
            log.error("Failed to retry push for ID: {}", pushDetailId, e);
            throw new RuntimeException("Failed to retry push: " + e.getMessage(), e);
        }
    }

    @Override
    public CompletableFuture<Boolean> sendEmailNotification(String email, String subject, String content) {
        log.info("Sending email notification to: {}", email);

        return CompletableFuture.supplyAsync(() -> {
            try {
                // TODO: Implement actual email sending logic
                // This is a placeholder implementation
                log.info("Email notification sent successfully to: {}", email);

                // Simulate email sending delay
                Thread.sleep(1000);

                // For now, always return success
                return true;
            } catch (Exception e) {
                log.error("Failed to send email to: {}", email, e);
                return false;
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> sendSmsNotification(String phoneNumber, String message) {
        log.info("Sending SMS notification to: {}", phoneNumber);

        return CompletableFuture.supplyAsync(() -> {
            try {
                HashMap<String, String> messageMap = new HashMap<>();
                messageMap.put("message", message);
                smsUtil.sendSms(phoneNumber, smsTemplateId, messageMap);
                // This is a placeholder implementation
                log.info("SMS notification sent successfully to: {}", phoneNumber);

                // For now, always return success
                return true;
            } catch (Exception e) {
                log.error("Failed to send SMS to: {}", phoneNumber, e);
                return false;
            }
        });
    }

    @Override
    public CompletableFuture<Boolean> sendSystemNotification(String username, String title, String content) {
        log.info("Sending system notification to: {}", username);

        return CompletableFuture.supplyAsync(() -> {
            try {
                // TODO: Implement actual system notification logic
                // This is a placeholder implementation
                log.info("System notification sent successfully to: {}", username);

                // Simulate system notification delay
                Thread.sleep(200);

                // For now, always return success
                return true;
            } catch (Exception e) {
                log.error("Failed to send system notification to: {}", username, e);
                return false;
            }
        });
    }

    @Override
    public CompletableFuture<List<AlertPushDetailResponseDto>> processPushNotifications(
            AlertResult alertResult, String username, String phoneNumber, String email) {

        // 针对时间段内的预警生成预警记录，

        // 针对预警记录生成预警邮件和短信

        // 发送邮件 赋予预警记录状态

        // 保存预警记录
        log.info("Processing push notifications for alert ID: {}", alertResult.getId());

        return CompletableFuture.supplyAsync(() -> {
            try {
                // Generate push records first
                List<AlertPushDetailResponseDto> pushRecords = generatePushRecordsForAlert(
                        alertResult, username, phoneNumber, email);

                // Process each push record asynchronously
                List<CompletableFuture<Void>> pushTasks = new ArrayList<>();

                for (AlertPushDetailResponseDto pushRecord : pushRecords) {
                    CompletableFuture<Void> pushTask = CompletableFuture.runAsync(() -> {
                        try {
                            boolean success = false;
                            String errorMessage = null;
                            String pushDetails = null;

                            switch (pushRecord.pushType()) {
                                case EMAIL:
                                    success = sendEmailNotification(
                                            pushRecord.accountInfo(),
                                            "预警通知: " + alertResult.getTitle(),
                                            alertResult.getContent()).get();
                                    pushDetails = success ? "邮件发送成功" : "邮件发送失败";
                                    break;
                                case SMS:
                                    success = sendSmsNotification(
                                            pushRecord.accountInfo(),
                                            "预警通知: " + alertResult.getTitle()).get();
                                    pushDetails = success ? "短信发送成功" : "短信发送失败";
                                    break;
                            }

                            // Update push record status
                            AlertPushDetail pushDetail = alertPushDetailRepository.findById(pushRecord.id())
                                    .orElseThrow(() -> new RuntimeException("Push detail not found"));

                            pushDetail.setPushStatus(success ? PushStatus.SUCCESS : PushStatus.FAILURE);
                            pushDetail.setErrorMessage(success ? null : errorMessage);
                            pushDetail.setPushDetails(pushDetails);
                            pushDetail.setPushTime(LocalDateTime.now());

                            alertPushDetailRepository.save(pushDetail);

                        } catch (Exception e) {
                            log.error("Failed to process push for record ID: {}", pushRecord.id(), e);
                        }
                    });
                    pushTasks.add(pushTask);
                }

                // Wait for all push tasks to complete
                CompletableFuture.allOf(pushTasks.toArray(new CompletableFuture[0])).join();

                // Return updated push records
                return getPushDetailsByAlertId(alertResult.getId());

            } catch (Exception e) {
                log.error("Failed to process push notifications for alert ID: {}", alertResult.getId(), e);
                throw new RuntimeException("Failed to process push notifications: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public long cleanupOldPushDetails(int daysToKeep) {
        log.info("Cleaning up push details older than {} days", daysToKeep);

        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysToKeep);

            // Count records to be deleted
            long countBefore = alertPushDetailRepository.count();

            // Delete old records
            alertPushDetailRepository.deleteOlderThan(cutoffDate);

            long countAfter = alertPushDetailRepository.count();
            long deletedCount = countBefore - countAfter;

            log.info("Successfully cleaned up {} old push detail records", deletedCount);
            return deletedCount;

        } catch (Exception e) {
            log.error("Failed to cleanup old push details", e);
            throw new RuntimeException("Failed to cleanup old push details: " + e.getMessage(), e);
        }
    }

    @Override
    public void createPushRecordsForAlert(AlertResult alert, Object recipient) {
        // This method creates push records for batch notifications
        // The actual push sending is handled separately in batch
        log.debug("Creating push records for alert {} and recipient", alert.getId());

        // For now, this is a placeholder implementation
        // In a real implementation, you would create AlertPushDetail records
        // but not actually send the notifications (since they're sent in batch)

        // The recipient parameter would be cast to ReceptionRulesEngine.RecipientInfo
        // and push records would be created based on the recipient's preferences
    }

}
