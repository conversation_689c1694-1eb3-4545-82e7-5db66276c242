package com.czb.hn.service.business;

import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.jpa.securadar.entity.AlertNotificationQueue;
import com.czb.hn.jpa.securadar.entity.AlertResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Alert Notification Scheduler Interface
 * Manages the scheduling and processing of alert notifications based on reception settings
 * Decouples alert generation from notification delivery
 */
public interface AlertNotificationScheduler {

    /**
     * Process pending notifications (scheduled task)
     * Checks for notifications ready to be sent and processes them
     */
    void processPendingNotifications();

    /**
     * Schedule notifications for a new alert
     * 
     * @param alert Alert result to schedule notifications for
     * @param configuration Alert configuration containing reception settings
     */
    void scheduleNotificationsForAlert(AlertResult alert, AlertConfigurationResponseDto configuration);

    /**
     * Schedule no-alert notifications for configurations without recent alerts
     * 
     * @param configuration Alert configuration to check for no-alert notifications
     */
    void scheduleNoAlertNotifications(AlertConfigurationResponseDto configuration);

    /**
     * Process notifications ready for sending
     * 
     * @return Number of notifications processed
     */
    int processReadyNotifications();

    /**
     * Retry failed notifications
     * 
     * @return Number of notifications retried
     */
    int retryFailedNotifications();

    /**
     * Clean up old notification records
     * 
     * @param daysToKeep Number of days to keep completed notifications
     * @return Number of records cleaned up
     */
    long cleanupOldNotifications(int daysToKeep);


    /**
     * Get notification queue statistics
     * 
     * @param enterpriseId Enterprise ID (null for all enterprises)
     * @return Notification statistics
     */
    NotificationStatistics getNotificationStatistics(String enterpriseId);



    /**
     * Get pending notifications for an enterprise
     * 
     * @param enterpriseId Enterprise ID
     * @param limit Maximum number of notifications to return
     * @return List of pending notifications
     */
    List<AlertNotificationQueue> getPendingNotifications(String enterpriseId, int limit);

    /**
     * Force process a specific notification
     * 
     * @param notificationId Notification ID
     * @return true if processed successfully
     */
    boolean forceProcessNotification(Long notificationId);

    /**
     * Notification statistics record
     */
    record NotificationStatistics(
        long pendingCount,
        long processingCount,
        long completedCount,
        long failedCount,
        long overdueCount,
        long stuckCount,
        double successRate,
        LocalDateTime oldestPending,
        LocalDateTime newestCompleted
    ) {
        /**
         * Get total notification count
         */
        public long getTotalCount() {
            return pendingCount + processingCount + completedCount + failedCount;
        }

        /**
         * Check if there are any issues requiring attention
         */
        public boolean hasIssues() {
            return overdueCount > 0 || stuckCount > 0 || successRate < 90.0;
        }

        /**
         * Get success rate as percentage
         */
        public double getSuccessRatePercentage() {
            long total = completedCount + failedCount;
            if (total == 0) {
                return 100.0;
            }
            return (double) completedCount / total * 100.0;
        }
    }

    /**
     * Notification processing result
     */
    record NotificationProcessingResult(
        int processedCount,
        int successCount,
        int failureCount,
        List<String> errors
    ) {
        /**
         * Check if processing was completely successful
         */
        public boolean isFullySuccessful() {
            return failureCount == 0 && errors.isEmpty();
        }

        /**
         * Get success rate for this processing batch
         */
        public double getSuccessRate() {
            if (processedCount == 0) {
                return 100.0;
            }
            return (double) successCount / processedCount * 100.0;
        }
    }
}
