package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.jpa.securadar.entity.AlertNotificationQueue;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.NotificationStatus;
import com.czb.hn.enums.NotificationType;
import com.czb.hn.jpa.securadar.repository.AlertNotificationQueueRepository;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.AlertConfigurationConsumerService;
import com.czb.hn.service.business.AlertNotificationScheduler;
import com.czb.hn.service.business.AlertPushService;
import com.czb.hn.service.business.ReceptionRulesEngine;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Alert Notification Scheduler Implementation
 * Handles the scheduling and processing of alert notifications based on
 * reception settings
 */
@Service
@Slf4j
public class AlertNotificationSchedulerImpl implements AlertNotificationScheduler {

    @Autowired
    private AlertNotificationQueueRepository notificationQueueRepository;

    @Autowired
    private AlertResultRepository alertResultRepository;

    @Autowired
    private AlertConfigurationConsumerService alertConfigConsumerService;

    @Autowired
    private ReceptionRulesEngine receptionRulesEngine;

    @Autowired
    private AlertPushService alertPushService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Scheduled(fixedDelay = 60000) // Every minute
    public void processPendingNotifications() {
        log.debug("Starting to process pending notifications");

        try {
            // Process ready notifications
            int processedCount = processReadyNotifications();

            // Retry failed notifications
            int retriedCount = retryFailedNotifications();

            // Schedule new notifications for recent alerts
            int scheduledCount = scheduleNewNotifications();

            // Check for no-alert notifications
            int noAlertCount = scheduleNoAlertNotifications();

            if (processedCount > 0 || retriedCount > 0 ||
                    scheduledCount > 0 || noAlertCount > 0) {
                log.info("Notification processing summary - Processed: {}, Retried: {}, , Scheduled: {}, NoAlert: {}",
                        processedCount, retriedCount, scheduledCount, noAlertCount);
            }

        } catch (Exception e) {
            log.error("Error in notification processing cycle", e);
        }
    }

    @Override
    public void scheduleNotificationsForAlert(AlertResult alert, AlertConfigurationResponseDto configuration) {
        log.debug("Scheduling notifications for alert ID: {} with configuration: {}",
                alert.getId(), configuration.id());

        try {
            if (configuration.receptionSettings() == null) {
                log.debug("No reception settings found for configuration: {}", configuration.id());
                return;
            }

            // Get last notification time for this configuration
            LocalDateTime lastNotificationTime = getLastNotificationTime(configuration.id());

            // Evaluate notification schedule
            ReceptionRulesEngine.NotificationScheduleResult scheduleResult = receptionRulesEngine
                    .evaluateNotificationSchedule(
                            alert, configuration.receptionSettings(), lastNotificationTime, LocalDateTime.now());

            if (!scheduleResult.shouldSchedule()) {
                log.debug("Notification not scheduled for alert {}: {}", alert.getId(), scheduleResult.reason());
                return;
            }

            // Extract recipients
            List<ReceptionRulesEngine.RecipientInfo> recipients = receptionRulesEngine
                    .extractRecipients(configuration.receptionSettings());

            if (recipients.isEmpty()) {
                log.warn("No recipients found for configuration: {}", configuration.id());
                return;
            }

            // Check if there's already a pending notification for this configuration within
            // the batch window
            LocalDateTime batchWindowStart = LocalDateTime.now().minusMinutes(
                    configuration.receptionSettings().alertInterval() != null
                            ? configuration.receptionSettings().alertInterval()
                            : 60);

            AlertNotificationQueue existingNotification = notificationQueueRepository
                    .findPendingNotificationInTimeWindow(
                            configuration.id(),
                            batchWindowStart,
                            LocalDateTime.now().plusMinutes(30))
                    .stream()
                    .findFirst()
                    .orElse(null);

            if (existingNotification != null) {
                // Associate this alert with existing batch notification
                alert.setAlertNotificationQueueId(existingNotification.getId());
                alertResultRepository.save(alert);
                log.info("Associated alert {} with existing batch notification {}", alert.getId(),
                        existingNotification.getId());
                return;
            }

            // Create new batch notification queue entry
            AlertNotificationQueue notification = AlertNotificationQueue.builder()
                    .planId(alert.getPlanId())
                    .configurationId(configuration.id())
                    .enterpriseId(alert.getEnterpriseId())
                    .scheduledTime(scheduleResult.scheduledTime())
                    .status(NotificationStatus.PENDING)
                    .recipients(serializeRecipients(recipients))
                    .notificationType(scheduleResult.notificationType())
                    .receptionSettings(serializeReceptionSettings(configuration.receptionSettings()))
                    .createdBy("SCHEDULER")
                    .build();

            AlertNotificationQueue savedNotification = notificationQueueRepository.save(notification);

            // Associate this alert with the new notification
            alert.setAlertNotificationQueueId(savedNotification.getId());
            alertResultRepository.save(alert);

            log.info("Scheduled new batch notification {} for alert {} at {}", savedNotification.getId(),
                    alert.getId(), scheduleResult.scheduledTime());

        } catch (Exception e) {
            log.error("Failed to schedule notifications for alert {}", alert.getId(), e);
        }
    }

    @Override
    public void scheduleNoAlertNotifications(AlertConfigurationResponseDto configuration) {
        log.debug("Checking no-alert notifications for configuration: {}", configuration.id());

        try {
            if (configuration.receptionSettings() == null ||
                    configuration.receptionSettings().noAlertNotification() == null ||
                    !configuration.receptionSettings().noAlertNotification()) {
                return; // No-alert notifications not enabled
            }

            // Check if there were any alerts in the last interval period
            LocalDateTime intervalStart = LocalDateTime.now().minusMinutes(
                    configuration.receptionSettings().alertInterval() != null
                            ? configuration.receptionSettings().alertInterval()
                            : 60);

            boolean hasRecentAlerts = alertResultRepository.existsByConfigurationIdAndWarningTimeBetween(
                    configuration.id(), intervalStart, LocalDateTime.now());

            if (hasRecentAlerts) {
                log.debug("Recent alerts found for configuration {}, no-alert notification not needed",
                        configuration.id());
                return;
            }

            // Check if no-alert notification already scheduled for this period
            if (notificationQueueRepository.existsNoAlertNotificationInTimeRange(
                    configuration.id(), intervalStart, LocalDateTime.now().plusHours(1))) {
                log.debug("No-alert notification already scheduled for configuration {}", configuration.id());
                return;
            }

            // Get last notification time
            LocalDateTime lastNotificationTime = getLastNotificationTime(configuration.id());

            // Evaluate notification schedule for no-alert case
            ReceptionRulesEngine.NotificationScheduleResult scheduleResult = receptionRulesEngine
                    .evaluateNotificationSchedule(
                            null, configuration.receptionSettings(), lastNotificationTime, LocalDateTime.now());

            if (!scheduleResult.shouldSchedule() || scheduleResult.notificationType() != NotificationType.NO_ALERT) {
                log.debug("No-alert notification not scheduled for configuration {}: {}",
                        configuration.id(), scheduleResult.reason());
                return;
            }

            // Extract recipients
            List<ReceptionRulesEngine.RecipientInfo> recipients = receptionRulesEngine
                    .extractRecipients(configuration.receptionSettings());

            if (recipients.isEmpty()) {
                log.warn("No recipients found for no-alert notification, configuration: {}", configuration.id());
                return;
            }

            // Create no-alert notification queue entry
            AlertNotificationQueue notification = AlertNotificationQueue.builder()
                    .planId(configuration.planId())
                    .configurationId(configuration.id())
                    .enterpriseId(configuration.enterpriseId())
                    .scheduledTime(scheduleResult.scheduledTime())
                    .status(NotificationStatus.PENDING)
                    .recipients(serializeRecipients(recipients))
                    .notificationType(NotificationType.NO_ALERT)
                    .receptionSettings(serializeReceptionSettings(configuration.receptionSettings()))
                    .createdBy("SCHEDULER")
                    .build();

            notificationQueueRepository.save(notification);
            log.info("Scheduled no-alert notification for configuration {} at {}",
                    configuration.id(), scheduleResult.scheduledTime());

        } catch (Exception e) {
            log.error("Failed to schedule no-alert notifications for configuration {}", configuration.id(), e);
        }
    }

    @Override
    public int processReadyNotifications() {
        log.debug("Processing ready notifications");

        try {
            List<AlertNotificationQueue> readyNotifications = notificationQueueRepository
                    .findReadyToProcess(NotificationStatus.PENDING, LocalDateTime.now());

            int processedCount = 0;
            for (AlertNotificationQueue notification : readyNotifications) {
                try {
                    processNotification(notification);
                    processedCount++;
                } catch (Exception e) {
                    log.error("Failed to process notification {}", notification.getId(), e);
                    notification.markAsFailed("Processing error: " + e.getMessage());
                    notificationQueueRepository.save(notification);
                }
            }

            return processedCount;

        } catch (Exception e) {
            log.error("Error processing ready notifications", e);
            return 0;
        }
    }

    @Override
    public int retryFailedNotifications() {
        log.debug("Retrying failed notifications");

        try {
            List<AlertNotificationQueue> retryNotifications = notificationQueueRepository
                    .findReadyForRetry(NotificationStatus.FAILED, LocalDateTime.now());

            int retriedCount = 0;
            for (AlertNotificationQueue notification : retryNotifications) {
                try {
                    processNotification(notification);
                    retriedCount++;
                } catch (Exception e) {
                    log.error("Failed to retry notification {}", notification.getId(), e);
                    notification.markAsFailed("Retry error: " + e.getMessage());
                    notificationQueueRepository.save(notification);
                }
            }

            return retriedCount;

        } catch (Exception e) {
            log.error("Error retrying failed notifications", e);
            return 0;
        }
    }

    /**
     * Process a single notification
     */
    private void processNotification(AlertNotificationQueue notification) {
        log.debug("Processing notification {}", notification.getId());

        try {
            // Mark as processing
            notification.markAsProcessing();
            notificationQueueRepository.save(notification);

            // Deserialize recipients
            List<ReceptionRulesEngine.RecipientInfo> recipients = deserializeRecipients(notification.getRecipients());

            // Process based on notification type
            if (notification.isNoAlertNotification()) {
                processNoAlertNotification(notification, recipients);
            } else {
                processAlertNotification(notification, recipients);
            }

            // Mark as completed
            notification.markAsCompleted();
            notificationQueueRepository.save(notification);

            log.info("Successfully processed notification {}", notification.getId());

        } catch (Exception e) {
            log.error("Failed to process notification {}", notification.getId(), e);
            notification.markAsFailed("Processing failed: " + e.getMessage());
            notificationQueueRepository.save(notification);
            throw e;
        }
    }

    /**
     * Process no-alert notification
     */
    private void processNoAlertNotification(AlertNotificationQueue notification,
            List<ReceptionRulesEngine.RecipientInfo> recipients) {

        log.debug("Processing no-alert notification for configuration {}", notification.getConfigurationId());

        // Create a dummy alert result for no-alert notification
        AlertResult dummyAlert = AlertResult.builder()
                .id(null) // No actual alert ID
                .enterpriseId(notification.getEnterpriseId())
                .configurationId(notification.getConfigurationId())
                .title("无预警通知")
                .content("在指定时间段内未检测到预警信息")
                .warningTime(notification.getScheduledTime())
                .build();

        // Process push notifications for each recipient
        for (ReceptionRulesEngine.RecipientInfo recipient : recipients) {
            try {
                if (recipient.emailEnabled() && recipient.email() != null) {
                    alertPushService.sendEmailNotification(
                            recipient.email(),
                            "无预警通知",
                            "在指定时间段内未检测到预警信息，系统运行正常。");
                }

                if (recipient.smsEnabled() && recipient.phone() != null) {
                    alertPushService.sendSmsNotification(
                            recipient.phone(),
                            "无预警通知：系统运行正常，未检测到预警信息。");
                }

            } catch (Exception e) {
                log.error("Failed to send no-alert notification to recipient {}", recipient.name(), e);
            }
        }
    }

    /**
     * Process alert-based notification
     */
    private void processAlertNotification(AlertNotificationQueue notification,
            List<ReceptionRulesEngine.RecipientInfo> recipients) {

        log.debug("Processing batch alert notification {}", notification.getId());

        // Get all alerts associated with this notification
        List<AlertResult> alerts = alertResultRepository.findByAlertNotificationQueueId(notification.getId());

        if (alerts.isEmpty()) {
            log.warn("No alerts found for notification queue {}", notification.getId());
            return;
        }

        log.info("Processing batch notification for {} alerts", alerts.size());

        // Send one notification per recipient type (SMS/Email), but create push records
        // for each alert
        for (ReceptionRulesEngine.RecipientInfo recipient : recipients) {
            try {
                // Send batch notification (one SMS/Email for all alerts)
                if (recipient.smsEnabled() && recipient.phone() != null) {
                    String batchMessage = createBatchSmsMessage(alerts);
                    alertPushService.sendSmsNotification(recipient.phone(), batchMessage);
                }

                if (recipient.emailEnabled() && recipient.email() != null) {
                    String batchSubject = createBatchEmailSubject(alerts);
                    String batchContent = createBatchEmailContent(alerts);
                    alertPushService.sendEmailNotification(recipient.email(), batchSubject, batchContent);
                }

                // Create push records for each alert
                for (AlertResult alert : alerts) {
                    alertPushService.createPushRecordsForAlert(alert, recipient);
                }

            } catch (Exception e) {
                log.error("Failed to send batch notification to recipient {}", recipient.name(), e);
            }
        }
    }

    /**
     * Get last notification time for a configuration
     */
    private LocalDateTime getLastNotificationTime(Long configurationId) {
        // Implementation would query for the most recent notification for this
        // configuration
        // For now, return null (no previous notification)
        return null;
    }

    /**
     * Create batch SMS message for multiple alerts
     */
    private String createBatchSmsMessage(List<AlertResult> alerts) {
        if (alerts.size() == 1) {
            AlertResult alert = alerts.get(0);
            return String.format("【预警通知】%s - %s",
                    alert.getTitle(),
                    alert.getWarningLevel().getDescription());
        } else {
            return String.format("【批量预警通知】共%d条预警，请及时查看处理", alerts.size());
        }
    }

    /**
     * Create batch email subject for multiple alerts
     */
    private String createBatchEmailSubject(List<AlertResult> alerts) {
        if (alerts.size() == 1) {
            return "【预警通知】" + alerts.get(0).getTitle();
        } else {
            return String.format("【批量预警通知】共%d条预警", alerts.size());
        }
    }

    /**
     * Create batch email content for multiple alerts
     */
    private String createBatchEmailContent(List<AlertResult> alerts) {
        StringBuilder content = new StringBuilder();
        content.append("您有新的预警信息需要处理：\n\n");

        for (int i = 0; i < alerts.size(); i++) {
            AlertResult alert = alerts.get(i);
            content.append(String.format("%d. 【%s】%s\n",
                    i + 1,
                    alert.getWarningLevel().getDescription(),
                    alert.getTitle()));
            content.append("   时间：").append(alert.getWarningTime()).append("\n");
            content.append("   来源：").append(alert.getSource()).append("\n\n");
        }

        content.append("请登录系统查看详细信息并及时处理。");
        return content.toString();
    }

    /**
     * Schedule new notifications for recent alerts
     */
    private int scheduleNewNotifications() {
        // Implementation would check for recent alerts that don't have notifications
        // scheduled
        // For now, return 0

        return 0;
    }

    /**
     * Schedule no-alert notifications for all active configurations
     */
    private int scheduleNoAlertNotifications() {
        try {
            List<AlertConfigurationResponseDto> activeConfigurations = alertConfigConsumerService
                    .getAllActiveConfigurations();

            int scheduledCount = 0;
            for (AlertConfigurationResponseDto config : activeConfigurations) {
                try {
                    scheduleNoAlertNotifications(config);
                    scheduledCount++;
                } catch (Exception e) {
                    log.error("Failed to schedule no-alert notifications for configuration {}", config.id(), e);
                }
            }

            return scheduledCount;

        } catch (Exception e) {
            log.error("Error scheduling no-alert notifications", e);
            return 0;
        }
    }

    /**
     * Serialize recipients to JSON
     */
    private String serializeRecipients(List<ReceptionRulesEngine.RecipientInfo> recipients) {
        try {
            return objectMapper.writeValueAsString(recipients);
        } catch (Exception e) {
            log.error("Failed to serialize recipients", e);
            return "[]";
        }
    }

    /**
     * Deserialize recipients from JSON
     */
    private List<ReceptionRulesEngine.RecipientInfo> deserializeRecipients(String recipientsJson) {
        try {
            return objectMapper.readValue(recipientsJson,
                    objectMapper.getTypeFactory().constructCollectionType(List.class,
                            ReceptionRulesEngine.RecipientInfo.class));
        } catch (Exception e) {
            log.error("Failed to deserialize recipients", e);
            return new ArrayList<>();
        }
    }

    /**
     * Serialize reception settings to JSON
     */
    private String serializeReceptionSettings(Object receptionSettings) {
        try {
            return objectMapper.writeValueAsString(receptionSettings);
        } catch (Exception e) {
            log.error("Failed to serialize reception settings", e);
            return "{}";
        }
    }

    // Additional methods would be implemented here...

    @Override
    public long cleanupOldNotifications(int daysToKeep) {
        // Implementation for cleanup
        return 0;
    }

    @Override
    public NotificationStatistics getNotificationStatistics(String enterpriseId) {
        // Implementation for statistics
        return new NotificationStatistics(0, 0, 0, 0, 0, 0, 100.0, null, null);
    }

    @Override
    public List<AlertNotificationQueue> getPendingNotifications(String enterpriseId, int limit) {
        // Implementation for getting pending notifications
        return new ArrayList<>();
    }

    @Override
    public boolean forceProcessNotification(Long notificationId) {
        // Implementation for force processing
        return false;
    }
}
