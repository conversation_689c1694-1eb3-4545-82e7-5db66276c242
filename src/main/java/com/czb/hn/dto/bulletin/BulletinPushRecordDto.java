package com.czb.hn.dto.bulletin;

import java.time.LocalDateTime;

/**
 * 简报推送记录DTO
 */
public record BulletinPushRecordDto(
    Long id,
    Long generationId,
    String account,
    String pushType,
    String pushMethod,
    LocalDateTime pushTime,
    String status
) {
    /**
     * 紧凑的规范构造函数，用于验证输入参数
     */
    public BulletinPushRecordDto {
        if (account == null || account.isBlank()) {
            throw new IllegalArgumentException("推送账号不能为空");
        }
        if (pushType == null || pushType.isBlank()) {
            throw new IllegalArgumentException("推送类型不能为空");
        }
        if (pushMethod == null || pushMethod.isBlank()) {
            throw new IllegalArgumentException("推送方式不能为空");
        }
    }
} 