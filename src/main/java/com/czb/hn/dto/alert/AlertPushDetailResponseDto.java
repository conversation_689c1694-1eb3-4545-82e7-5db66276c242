package com.czb.hn.dto.alert;

import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * Alert Push Detail Response DTO
 * Contains complete push detail data for API responses
 */
public record AlertPushDetailResponseDto(
    @Schema(description = "推送详情ID", example = "1")
    Long id,

    @Schema(description = "预警ID", example = "1")
    Long alertId,

    @Schema(description = "企业ID", example = "enterprise123")
    String enterpriseId,

    @Schema(description = "账户信息", example = "<EMAIL>")
    String accountInfo,

    @Schema(description = "推送类型", example = "EMAIL", allowableValues = {"EMAIL", "SMS", "SYSTEM"})
    PushType pushType,

    @Schema(description = "推送状态", example = "SUCCESS", allowableValues = {"SUCCESS", "FAILURE"})
    PushStatus pushStatus,

    @Schema(description = "推送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime pushTime,

    @Schema(description = "错误信息", example = "邮件发送失败：无效的邮箱地址")
    String errorMessage,

    @Schema(description = "推送详情", example = "邮件已成功发送到 <EMAIL>")
    String pushDetails,

    @Schema(description = "重试次数", example = "0")
    Integer retryCount,

    @Schema(description = "最后重试时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime lastRetryTime,

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime createdAt,

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime updatedAt,

    @Schema(description = "创建人", example = "admin")
    String createdBy
) {
    /**
     * Check if this push was successful
     */
    public boolean isSuccessful() {
        return pushStatus == PushStatus.SUCCESS;
    }

    /**
     * Check if this push failed
     */
    public boolean isFailed() {
        return pushStatus == PushStatus.FAILURE;
    }

    /**
     * Check if this push can be retried
     */
    public boolean canRetry() {
        return isFailed() && retryCount < 3;
    }

    /**
     * Get push type description in Chinese
     */
    public String getPushTypeDescription() {
        return pushType != null ? pushType.getDescription() : null;
    }

    /**
     * Get push status description in Chinese
     */
    public String getPushStatusDescription() {
        return pushStatus != null ? pushStatus.getDescription() : null;
    }
}
