package com.czb.hn.dto.alert;

import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * Alert Push Detail Create DTO
 * Used for creating new push detail records
 */
public record AlertPushDetailCreateDto(
    @Schema(description = "预警ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "Alert ID cannot be null")
    Long alertId,

    @Schema(description = "方案id", example = "1234567890")
    @NotNull(message = "Plan ID cannot be null")
    Long planId,

    @Schema(description = "预警配置快照ID", example = "987654321")
    @NotNull(message = "Alert configuration snapshot ID cannot be null")
    Long alertConfigSnapshotId,

    @Schema(description = "企业ID", example = "enterprise123", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "Enterprise ID cannot be blank")
    @Size(max = 255, message = "Enterprise ID cannot exceed 255 characters")
    String enterpriseId,

    @Schema(description = "账户信息", example = "<EMAIL>", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "Account info cannot be blank")
    @Size(max = 255, message = "Account info cannot exceed 255 characters")
    String accountInfo,

    @Schema(description = "推送类型", example = "EMAIL", allowableValues = {"EMAIL", "SMS", "SYSTEM"}, 
            requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "Push type cannot be null")
    PushType pushType,

    @Schema(description = "推送状态", example = "SUCCESS", allowableValues = {"SUCCESS", "FAILURE"}, 
            requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "Push status cannot be null")
    PushStatus pushStatus,

    @Schema(description = "推送时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "Push time cannot be null")
    LocalDateTime pushTime,

    @Schema(description = "错误信息", example = "邮件发送失败：无效的邮箱地址")
    @Size(max = 1000, message = "Error message cannot exceed 1000 characters")
    String errorMessage,

    @Schema(description = "推送详情", example = "邮件已成功发送到 <EMAIL>")
    @Size(max = 1000, message = "Push details cannot exceed 1000 characters")
    String pushDetails,

    @Schema(description = "创建人", example = "admin")
    @Size(max = 255, message = "Created by cannot exceed 255 characters")
    String createdBy
) {
    // Compact canonical constructor for validation
    public AlertPushDetailCreateDto {
        // Validate push type and status combination
        if (pushStatus == PushStatus.FAILURE && (errorMessage == null || errorMessage.isBlank())) {
            throw new IllegalArgumentException("Error message is required when push status is FAILURE");
        }

        // Validate account info format based on push type
        if (pushType == PushType.EMAIL && accountInfo != null) {
            if (!accountInfo.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")) {
                throw new IllegalArgumentException("Invalid email address format for EMAIL push type");
            }
        } else if (pushType == PushType.SMS && accountInfo != null) {
            if (!accountInfo.matches("^1[3-9]\\d{9}$")) {
                throw new IllegalArgumentException("Invalid phone number format for SMS push type (must be Chinese mobile number)");
            }
        }

        // Validate push time is not in the future
        if (pushTime != null && pushTime.isAfter(LocalDateTime.now())) {
            throw new IllegalArgumentException("Push time cannot be in the future");
        }
    }
}
