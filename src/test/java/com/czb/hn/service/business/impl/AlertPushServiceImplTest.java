package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.AlertPushDetailCreateDto;
import com.czb.hn.dto.alert.AlertPushDetailResponseDto;
import com.czb.hn.jpa.securadar.entity.AlertPushDetail;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import com.czb.hn.jpa.securadar.repository.AlertPushDetailRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for AlertPushServiceImpl
 */
@ExtendWith(MockitoExtension.class)
class AlertPushServiceImplTest {

    @Mock
    private AlertPushDetailRepository alertPushDetailRepository;

    @InjectMocks
    private AlertPushServiceImpl alertPushService;

    private AlertPushDetailCreateDto createDto;
    private AlertPushDetail pushDetail;
    private AlertResult alertResult;

    @BeforeEach
    void setUp() {
        LocalDateTime now = LocalDateTime.now();

        createDto = new AlertPushDetailCreateDto(
                1L,
                "enterprise123",
                "<EMAIL>",
                PushType.EMAIL,
                PushStatus.SUCCESS,
                now,
                null,
                "邮件发送成功",
                "admin");

        pushDetail = AlertPushDetail.builder()
                .id(1L)
                .alertId(1L)
                .enterpriseId("enterprise123")
                .accountInfo("<EMAIL>")
                .pushType(PushType.EMAIL)
                .pushStatus(PushStatus.SUCCESS)
                .pushTime(now)
                .pushDetails("邮件发送成功")
                .retryCount(0)
                .createdBy("admin")
                .createdAt(now)
                .updatedAt(now)
                .build();

        alertResult = AlertResult.builder()
                .id(1L)
                .enterpriseId("enterprise123")
                .title("测试预警")
                .content("测试预警内容")
                .warningTime(now)
                .build();
    }


    @Test
    void testGeneratePushRecordsForAlert_AllRecipients() {
        // Given
        when(alertPushDetailRepository.save(any(AlertPushDetail.class))).thenReturn(pushDetail);

        // When
        List<AlertPushDetailResponseDto> results = alertPushService.generatePushRecordsForAlert(
                alertResult, "admin", "***********", "<EMAIL>");

        // Then
        assertEquals(3, results.size());
        verify(alertPushDetailRepository, times(3)).save(any(AlertPushDetail.class));
    }

    @Test
    void testGeneratePushRecordsForAlert_PartialRecipients() {
        // Given
        when(alertPushDetailRepository.save(any(AlertPushDetail.class))).thenReturn(pushDetail);

        // When
        List<AlertPushDetailResponseDto> results = alertPushService.generatePushRecordsForAlert(
                alertResult, "admin", null, "<EMAIL>");

        // Then
        assertEquals(2, results.size()); // Only username and email
        verify(alertPushDetailRepository, times(2)).save(any(AlertPushDetail.class));
    }


    @Test
    void testRetryPush_Success() {
        // Given
        AlertPushDetail failedPushDetail = AlertPushDetail.builder()
                .id(1L)
                .alertId(1L)
                .enterpriseId("enterprise123")
                .accountInfo("<EMAIL>")
                .pushType(PushType.EMAIL)
                .pushStatus(PushStatus.FAILURE)
                .retryCount(1)
                .build();

        when(alertPushDetailRepository.findById(1L)).thenReturn(Optional.of(failedPushDetail));
        when(alertPushDetailRepository.save(any(AlertPushDetail.class))).thenReturn(failedPushDetail);

        // When
        AlertPushDetailResponseDto result = alertPushService.retryPush(1L);

        // Then
        assertNotNull(result);
        verify(alertPushDetailRepository).findById(1L);
        verify(alertPushDetailRepository).save(any(AlertPushDetail.class));
    }

    @Test
    void testRetryPush_NotFound() {
        // Given
        when(alertPushDetailRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            alertPushService.retryPush(1L);
        });

        assertTrue(exception.getMessage().contains("Failed to retry push"));
    }

    @Test
    void testCleanupOldPushDetails() {
        // Given
        when(alertPushDetailRepository.count()).thenReturn(100L, 80L);

        // When
        long deletedCount = alertPushService.cleanupOldPushDetails(30);

        // Then
        assertEquals(20L, deletedCount);
        verify(alertPushDetailRepository).deleteOlderThan(any(LocalDateTime.class));
    }

    @Test
    void testSendEmailNotification() {
        // When
        var future = alertPushService.sendEmailNotification(
                "<EMAIL>", "Test Subject", "Test Content");

        // Then
        assertNotNull(future);
        // Note: This is a placeholder implementation that always returns true
        assertTrue(future.join());
    }

    @Test
    void testSendSmsNotification() {
        // When
        var future = alertPushService.sendSmsNotification("***********", "Test Message");

        // Then
        assertNotNull(future);
        // Note: This is a placeholder implementation that always returns true
        assertTrue(future.join());
    }

    @Test
    void testSendSystemNotification() {
        // When
        var future = alertPushService.sendSystemNotification("admin", "Test Title", "Test Content");

        // Then
        assertNotNull(future);
        // Note: This is a placeholder implementation that always returns true
        assertTrue(future.join());
    }
}
