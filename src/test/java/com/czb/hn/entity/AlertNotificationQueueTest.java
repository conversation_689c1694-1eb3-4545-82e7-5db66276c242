package com.czb.hn.entity;

import com.czb.hn.jpa.securadar.entity.AlertNotificationQueue;
import com.czb.hn.enums.NotificationStatus;
import com.czb.hn.enums.NotificationType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for AlertNotificationQueue entity
 */
class AlertNotificationQueueTest {

    private AlertNotificationQueue notification;

    @BeforeEach
    void setUp() {
        notification = AlertNotificationQueue.builder()
            .id(1L)
            .alertId(1L)
            .configurationId(1L)
            .enterpriseId("enterprise123")
            .scheduledTime(LocalDateTime.now().plusMinutes(5))
            .status(NotificationStatus.PENDING)
            .notificationType(NotificationType.ALERT)
            .recipients("{\"emails\":[\"<EMAIL>\"]}")
            .attemptCount(0)
            .build();
    }

    @Test
    void testIsReadyToProcess_Ready() {
        // Given
        notification.setScheduledTime(LocalDateTime.now().minusMinutes(1));
        notification.setStatus(NotificationStatus.PENDING);
        notification.setAttemptCount(0);

        // When & Then
        assertTrue(notification.isReadyToProcess());
    }

    @Test
    void testIsReadyToProcess_FutureScheduledTime() {
        // Given
        notification.setScheduledTime(LocalDateTime.now().plusMinutes(5));
        notification.setStatus(NotificationStatus.PENDING);
        notification.setAttemptCount(0);

        // When & Then
        assertFalse(notification.isReadyToProcess());
    }

    @Test
    void testIsReadyToProcess_MaxAttemptsReached() {
        // Given
        notification.setScheduledTime(LocalDateTime.now().minusMinutes(1));
        notification.setStatus(NotificationStatus.PENDING);
        notification.setAttemptCount(3);

        // When & Then
        assertFalse(notification.isReadyToProcess());
    }

    @Test
    void testIsReadyToProcess_NotPending() {
        // Given
        notification.setScheduledTime(LocalDateTime.now().minusMinutes(1));
        notification.setStatus(NotificationStatus.PROCESSING);
        notification.setAttemptCount(0);

        // When & Then
        assertFalse(notification.isReadyToProcess());
    }

    @Test
    void testCanRetry_CanRetry() {
        // Given
        notification.setStatus(NotificationStatus.FAILED);
        notification.setAttemptCount(1);
        notification.setNextRetryTime(LocalDateTime.now().minusMinutes(1));

        // When & Then
        assertTrue(notification.canRetry());
    }

    @Test
    void testCanRetry_MaxAttemptsReached() {
        // Given
        notification.setStatus(NotificationStatus.FAILED);
        notification.setAttemptCount(3);

        // When & Then
        assertFalse(notification.canRetry());
    }

    @Test
    void testCanRetry_NotFailed() {
        // Given
        notification.setStatus(NotificationStatus.COMPLETED);
        notification.setAttemptCount(1);

        // When & Then
        assertFalse(notification.canRetry());
    }

    @Test
    void testCanRetry_FutureRetryTime() {
        // Given
        notification.setStatus(NotificationStatus.FAILED);
        notification.setAttemptCount(1);
        notification.setNextRetryTime(LocalDateTime.now().plusMinutes(5));

        // When & Then
        assertFalse(notification.canRetry());
    }

    @Test
    void testMarkAsProcessing() {
        // Given
        int initialAttemptCount = notification.getAttemptCount();
        LocalDateTime beforeProcessing = LocalDateTime.now();

        // When
        notification.markAsProcessing();

        // Then
        assertEquals(NotificationStatus.PROCESSING, notification.getStatus());
        assertEquals(initialAttemptCount + 1, notification.getAttemptCount());
        assertNotNull(notification.getProcessedAt());
        assertTrue(notification.getProcessedAt().isAfter(beforeProcessing) || 
                  notification.getProcessedAt().equals(beforeProcessing));
    }

    @Test
    void testMarkAsCompleted() {
        // Given
        notification.setLastError("Previous error");
        notification.setNextRetryTime(LocalDateTime.now().plusMinutes(5));
        LocalDateTime beforeCompletion = LocalDateTime.now();

        // When
        notification.markAsCompleted();

        // Then
        assertEquals(NotificationStatus.COMPLETED, notification.getStatus());
        assertNotNull(notification.getProcessedAt());
        assertTrue(notification.getProcessedAt().isAfter(beforeCompletion) || 
                  notification.getProcessedAt().equals(beforeCompletion));
        assertNull(notification.getLastError());
        assertNull(notification.getNextRetryTime());
    }

    @Test
    void testMarkAsFailed() {
        // Given
        String errorMessage = "Processing failed";
        notification.setAttemptCount(1);
        LocalDateTime beforeFailure = LocalDateTime.now();

        // When
        notification.markAsFailed(errorMessage);

        // Then
        assertEquals(NotificationStatus.FAILED, notification.getStatus());
        assertEquals(errorMessage, notification.getLastError());
        assertNotNull(notification.getNextRetryTime());
        assertTrue(notification.getNextRetryTime().isAfter(beforeFailure));
    }

    @Test
    void testMarkAsFailed_ExponentialBackoff() {
        // Given
        notification.setAttemptCount(2);

        // When
        notification.markAsFailed("Error");

        // Then
        // For attempt count 2, delay should be 2^2 * 5 = 20 minutes
        LocalDateTime expectedRetryTime = LocalDateTime.now().plusMinutes(20);
        assertTrue(notification.getNextRetryTime().isAfter(expectedRetryTime.minusMinutes(1)));
        assertTrue(notification.getNextRetryTime().isBefore(expectedRetryTime.plusMinutes(1)));
    }

    @Test
    void testMarkAsFailed_MaxAttemptsReached() {
        // Given
        notification.setAttemptCount(3);

        // When
        notification.markAsFailed("Error");

        // Then
        assertEquals(NotificationStatus.FAILED, notification.getStatus());
        assertEquals("Error", notification.getLastError());
        // No retry time should be set when max attempts reached
        assertNull(notification.getNextRetryTime());
    }

    @Test
    void testIsNoAlertNotification() {
        // Given
        notification.setAlertId(null);
        notification.setNotificationType(NotificationType.NO_ALERT);

        // When & Then
        assertTrue(notification.isNoAlertNotification());
        assertFalse(notification.isAlertBasedNotification());
    }

    @Test
    void testIsAlertBasedNotification() {
        // Given
        notification.setAlertId(1L);
        notification.setNotificationType(NotificationType.ALERT);

        // When & Then
        assertTrue(notification.isAlertBasedNotification());
        assertFalse(notification.isNoAlertNotification());
    }

    @Test
    void testGetNotificationDescription() {
        // Test no-alert notification
        notification.setAlertId(null);
        notification.setNotificationType(NotificationType.NO_ALERT);
        assertEquals("无预警通知", notification.getNotificationDescription());

        // Test info push notification
        notification.setAlertId(1L);
        notification.setNotificationType(NotificationType.INFO_PUSH);
        assertEquals("信息补推", notification.getNotificationDescription());

        // Test alert notification
        notification.setAlertId(1L);
        notification.setNotificationType(NotificationType.ALERT);
        assertEquals("预警通知", notification.getNotificationDescription());
    }

    @Test
    void testBuilder() {
        // When
        AlertNotificationQueue built = AlertNotificationQueue.builder()
            .alertId(null) // No-alert notification
            .configurationId(2L)
            .enterpriseId("enterprise456")
            .scheduledTime(LocalDateTime.now())
            .notificationType(NotificationType.NO_ALERT)
            .recipients("{\"usernames\":[\"admin\"]}")
            .build();

        // Then
        assertNull(built.getAlertId());
        assertEquals(2L, built.getConfigurationId());
        assertEquals("enterprise456", built.getEnterpriseId());
        assertEquals(NotificationType.NO_ALERT, built.getNotificationType());
        assertEquals(NotificationStatus.PENDING, built.getStatus()); // Default value
        assertEquals(0, built.getAttemptCount()); // Default value
    }

    @Test
    void testDefaultValues() {
        // When
        AlertNotificationQueue defaultNotification = AlertNotificationQueue.builder().build();

        // Then
        assertEquals(NotificationStatus.PENDING, defaultNotification.getStatus());
        assertEquals(0, defaultNotification.getAttemptCount());
    }
}
