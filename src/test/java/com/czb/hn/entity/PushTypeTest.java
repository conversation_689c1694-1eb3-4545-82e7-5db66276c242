package com.czb.hn.entity;

import com.czb.hn.enums.PushType;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for PushType enum
 */
class PushTypeTest {

    @Test
    void testGetDescription() {
        assertEquals("邮件", PushType.EMAIL.getDescription());
        assertEquals("短信", PushType.SMS.getDescription());
        assertEquals("系统", PushType.SYSTEM.getDescription());
    }

    @Test
    void testFromString_ValidValues() {
        assertEquals(PushType.EMAIL, PushType.fromString("EMAIL"));
        assertEquals(PushType.SMS, PushType.fromString("SMS"));
        assertEquals(PushType.SYSTEM, PushType.fromString("SYSTEM"));

        // Test case insensitive
        assertEquals(PushType.EMAIL, PushType.fromString("email"));
        assertEquals(PushType.SMS, PushType.fromString("sms"));
        assertEquals(PushType.SYSTEM, PushType.fromString("system"));
    }

    @Test
    void testFromString_InvalidValue() {
        // Smart conversion returns default value instead of throwing exception
        PushType result = PushType.fromString("INVALID");
        assertEquals(PushType.SYSTEM, result);
    }

    @Test
    void testFromString_NullValue() {
        // Smart conversion returns default value instead of throwing exception
        PushType result = PushType.fromString(null);
        assertEquals(PushType.SYSTEM, result);
    }

    @Test
    void testFromString_BlankValue() {
        // Smart conversion returns default value instead of throwing exception
        PushType result = PushType.fromString("   ");
        assertEquals(PushType.SYSTEM, result);
    }

    @Test
    void testFromString_EmptyValue() {
        // Smart conversion returns default value instead of throwing exception
        PushType result = PushType.fromString("");
        assertEquals(PushType.SYSTEM, result);
    }

    @Test
    void testEnumValues() {
        PushType[] values = PushType.values();
        assertEquals(3, values.length);

        assertTrue(java.util.Arrays.asList(values).contains(PushType.EMAIL));
        assertTrue(java.util.Arrays.asList(values).contains(PushType.SMS));
        assertTrue(java.util.Arrays.asList(values).contains(PushType.SYSTEM));
    }

    @Test
    void testValueOf() {
        assertEquals(PushType.EMAIL, PushType.valueOf("EMAIL"));
        assertEquals(PushType.SMS, PushType.valueOf("SMS"));
        assertEquals(PushType.SYSTEM, PushType.valueOf("SYSTEM"));
    }
}
