package com.czb.hn.dto.alert;

import com.czb.hn.enums.PushStatus;
import com.czb.hn.enums.PushType;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for AlertPushDetailCreateDto
 */
class AlertPushDetailCreateDtoTest {

    @Test
    void testValidDto_Success() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();
        
        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> {
            new AlertPushDetailCreateDto(
                1L,
                "enterprise123",
                "<EMAIL>",
                PushType.EMAIL,
                PushStatus.SUCCESS,
                pushTime,
                null,
                "邮件发送成功",
                "admin"
            );
        });
    }

    @Test
    void testValidDto_FailureWithErrorMessage() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();
        
        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> {
            new AlertPushDetailCreateDto(
                1L,
                "enterprise123",
                "<EMAIL>",
                PushType.EMAIL,
                PushStatus.FAILURE,
                pushTime,
                "邮件发送失败",
                "邮件发送失败详情",
                "admin"
            );
        });
    }

    @Test
    void testInvalidDto_FailureWithoutErrorMessage() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();
        
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            new AlertPushDetailCreateDto(
                1L,
                "enterprise123",
                "<EMAIL>",
                PushType.EMAIL,
                PushStatus.FAILURE,
                pushTime,
                null, // No error message for failure
                "邮件发送失败详情",
                "admin"
            );
        });
        
        assertEquals("Error message is required when push status is FAILURE", exception.getMessage());
    }

    @Test
    void testInvalidDto_FailureWithBlankErrorMessage() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();
        
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            new AlertPushDetailCreateDto(
                1L,
                "enterprise123",
                "<EMAIL>",
                PushType.EMAIL,
                PushStatus.FAILURE,
                pushTime,
                "   ", // Blank error message for failure
                "邮件发送失败详情",
                "admin"
            );
        });
        
        assertEquals("Error message is required when push status is FAILURE", exception.getMessage());
    }

    @Test
    void testInvalidDto_InvalidEmailFormat() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();
        
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            new AlertPushDetailCreateDto(
                1L,
                "enterprise123",
                "invalid-email", // Invalid email format
                PushType.EMAIL,
                PushStatus.SUCCESS,
                pushTime,
                null,
                "邮件发送成功",
                "admin"
            );
        });
        
        assertTrue(exception.getMessage().contains("Invalid email address format"));
    }

    @Test
    void testInvalidDto_InvalidPhoneFormat() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();
        
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            new AlertPushDetailCreateDto(
                1L,
                "enterprise123",
                "12345", // Invalid phone format
                PushType.SMS,
                PushStatus.SUCCESS,
                pushTime,
                null,
                "短信发送成功",
                "admin"
            );
        });
        
        assertTrue(exception.getMessage().contains("Invalid phone number format"));
    }

    @Test
    void testInvalidDto_FuturePushTime() {
        // Given
        LocalDateTime futurePushTime = LocalDateTime.now().plusHours(1);
        
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            new AlertPushDetailCreateDto(
                1L,
                "enterprise123",
                "<EMAIL>",
                PushType.EMAIL,
                PushStatus.SUCCESS,
                futurePushTime,
                null,
                "邮件发送成功",
                "admin"
            );
        });
        
        assertEquals("Push time cannot be in the future", exception.getMessage());
    }

    @Test
    void testValidDto_ValidEmailFormats() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();
        String[] validEmails = {
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        };
        
        // When & Then
        for (String email : validEmails) {
            assertDoesNotThrow(() -> {
                new AlertPushDetailCreateDto(
                    1L,
                    "enterprise123",
                    email,
                    PushType.EMAIL,
                    PushStatus.SUCCESS,
                    pushTime,
                    null,
                    "邮件发送成功",
                    "admin"
                );
            }, "Should accept valid email: " + email);
        }
    }

    @Test
    void testValidDto_ValidPhoneFormats() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();
        String[] validPhones = {
            "13800138000",
            "15912345678",
            "18612345678",
            "19912345678"
        };
        
        // When & Then
        for (String phone : validPhones) {
            assertDoesNotThrow(() -> {
                new AlertPushDetailCreateDto(
                    1L,
                    "enterprise123",
                    phone,
                    PushType.SMS,
                    PushStatus.SUCCESS,
                    pushTime,
                    null,
                    "短信发送成功",
                    "admin"
                );
            }, "Should accept valid phone: " + phone);
        }
    }

    @Test
    void testValidDto_SystemPushType() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();
        
        // When & Then - System push type should not validate account info format
        assertDoesNotThrow(() -> {
            new AlertPushDetailCreateDto(
                1L,
                "enterprise123",
                "admin", // Any username format for SYSTEM type
                PushType.SYSTEM,
                PushStatus.SUCCESS,
                pushTime,
                null,
                "系统通知发送成功",
                "admin"
            );
        });
    }

    @Test
    void testRecordMethods() {
        // Given
        LocalDateTime pushTime = LocalDateTime.now();
        AlertPushDetailCreateDto dto = new AlertPushDetailCreateDto(
            1L,
            "enterprise123",
            "<EMAIL>",
            PushType.EMAIL,
            PushStatus.SUCCESS,
            pushTime,
            null,
            "邮件发送成功",
            "admin"
        );
        
        // When & Then
        assertEquals(1L, dto.alertId());
        assertEquals("enterprise123", dto.enterpriseId());
        assertEquals("<EMAIL>", dto.accountInfo());
        assertEquals(PushType.EMAIL, dto.pushType());
        assertEquals(PushStatus.SUCCESS, dto.pushStatus());
        assertEquals(pushTime, dto.pushTime());
        assertNull(dto.errorMessage());
        assertEquals("邮件发送成功", dto.pushDetails());
        assertEquals("admin", dto.createdBy());
    }
}
