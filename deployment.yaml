apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: securadar
  name: securadar
  namespace: hncapital
spec:
  progressDeadlineSeconds: 30
  replicas: 1
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app: securadar
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: securadar
    spec:
      containers:
        - env:
            - name: JAVA_OPTS
              value: -Duser.timezone=GMT+08 -Dserver.port=8080 -Dspring.profiles.active=test
                -Deureka.client.enabled=false -Dlogging.config= -Dlogging.pattern.console=${LOG_PATTERN}
                -Dspring.jpa.show-sql=false -Dmanagement.health.mail.enabled=false -Dspring.datasource.url=jdbc:mysql://${MYSQL_HOST}:${MYSQL_PORT}/securadar?characterEncoding=utf8&useSSL=false
                -Dspring.datasource.username=${MYSQL_USERNAME} -Dspring.datasource.password=${MYSQL_PASSWORD}
                -Delasticsearch.host=${ELASTICSEARCH_HOST} -Delasticsearch.port=${ELASTICSEARCH_PORT} -Delasticsearch.username=${ELASTICSEARCH_USERNAME} -Delasticsearch.password=${ELASTICSEARCH_PASSWORD}
                -Dsms.url=${SMS_URL:http://hnczb-sms-svc:8080/api/smsnotice} -Dsms.newUrl=${SMS_NEW_URL:http://hnczb-sms-svc:8080/api/sms}
            - name: LANGUAGE
              value: en_US:en
            - name: LC_ALL
              value: en_US.UTF-8
            - name: LOG_PATTERN
              value: '%d{yyyy-MM-dd HH:mm:ss.SSS} %level [%cn] [%thread] %logger{32}:
            %msg%n'
            - name: RUN_ENV
              value: k8s
            - name: MYSQL_HOST
              valueFrom:
                secretKeyRef:
                  key: host
                  name: ycloud-mysql-config
                  optional: false
            - name: MYSQL_PORT
              valueFrom:
                secretKeyRef:
                  key: port
                  name: ycloud-mysql-config
                  optional: false
            - name: MYSQL_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: ycloud-mysql-config
                  optional: false
            - name: MYSQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: ycloud-mysql-config
                  optional: false
            - name: ELASTICSEARCH_HOST
              valueFrom:
                secretKeyRef:
                  key: host
                  name: ycloud-es-config
                  optional: false
            - name: ELASTICSEARCH_PORT
              valueFrom:
                secretKeyRef:
                  key: port
                  name: ycloud-es-config
                  optional: false
            - name: ELASTICSEARCH_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: ycloud-es-config
                  optional: false
            - name: ELASTICSEARCH_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: ycloud-es-config
                  optional: false
            - name: SMS_URL
              valueFrom:
                secretKeyRef:
                  key: sms-url
                  name: ycloud-sms-config
                  optional: false
            - name: SMS_NEW_URL
              valueFrom:
                secretKeyRef:
                  key: sms-new-url
                  name: ycloud-sms-config
                  optional: false
            - name: buildNum
              value: "BUILDNUM"
          image: registry.hn.com/library/ycjf-jdk:21-alpine3.19
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 6
            initialDelaySeconds: 30
            periodSeconds: 5
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 5
          name: s2i-java
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          readinessProbe:
            failureThreshold: 6
            initialDelaySeconds: 30
            periodSeconds: 5
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 5
          resources:
            limits:
              memory: 2Gi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /etc/localtime
              name: volume-localtime
            - mountPath: /deployments
              name: volume-app
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: harbor
      initContainers:
        - args:
            - -c
            - cp /app/*.jar /deployments
          command:
            - /bin/sh
          image: registry.hn.com/hncapital/securadar:YCPRTAG
          imagePullPolicy: Always
          name: securadar-0
          volumeMounts:
            - mountPath: /etc/localtime
              name: volume-localtime
            - mountPath: /deployments
              name: volume-app
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - hostPath:
            path: /etc/localtime
            type: ""
          name: volume-localtime
        - emptyDir: {}
          name: volume-app